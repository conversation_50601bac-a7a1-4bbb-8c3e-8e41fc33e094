{"name": "sela-web", "version": "1.0.0", "description": "SELA Educational Platform - React + Supabase + VPS", "type": "module", "scripts": {"dev": "npm run dev:frontend", "dev:frontend": "cd frontend && npm run dev", "dev:server": "cd server && npm run dev", "dev:all": "concurrently \"npm run dev:frontend\" \"npm run dev:server\"", "build": "npm run build:frontend && npm run build:server", "build:frontend": "cd frontend && npm run build", "build:server": "cd server && npm run build", "install:all": "npm install && cd frontend && npm install && cd ../server && npm install", "clean": "rm -rf node_modules frontend/node_modules server/node_modules", "test": "npm run test:frontend && npm run test:server", "test:frontend": "cd frontend && npm run test", "test:server": "cd server && npm run test"}, "keywords": ["education", "react", "supabase", "vps", "typescript", "learning-platform"], "author": "SELA Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "workspaces": ["frontend", "server"]}