# SELA_Web - Educational Platform

**Tech Stack:** React + Supabase + VPS  
**Architecture:** Feature-based, Easy Refactoring, Cost-Effective

## 🚀 Quick Start

### Prerequisites
- Node.js 20+
- npm or yarn
- Supabase account
- VPS server (for production)

### Development Setup

```bash
# Install all dependencies
npm run install:all

# Start development servers
npm run dev:all

# Or start individually
npm run dev:frontend  # Frontend on :3000
npm run dev:server    # Backend on :3001
```

### Environment Variables

Create `.env` files in both `frontend/` and `server/` directories:

#### Frontend (.env)
```env
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
VITE_API_URL=http://localhost:3001
```

#### Server (.env)
```env
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
REDIS_HOST=localhost
REDIS_PORT=6379
PORT=3001
```

## 📁 Project Structure

```
SELA_Web/
├── frontend/           # React + Vite frontend
│   ├── src/
│   │   ├── features/   # Feature modules
│   │   ├── shared/     # Shared components
│   │   ├── services/   # API services
│   │   ├── lib/        # Third-party configs
│   │   └── app/        # App configuration
├── server/             # Node.js API server
│   ├── src/
│   │   ├── routes/     # API routes
│   │   ├── services/   # Business logic
│   │   ├── middleware/ # Express middleware
│   │   └── config/     # Server configuration
├── docs/               # Documentation
└── web-bundles/        # Bundle configurations
```

## 🎯 Features

- [ ] Authentication & User Management
- [ ] Course Management System
- [ ] Video Player with Progress Tracking
- [ ] User Dashboard & Analytics
- [ ] File Upload & Storage
- [ ] BMad AI Integration
- [ ] Payment System
- [ ] Real-time Features

## 🛠️ Development

### Frontend
- **Framework:** React 18 + TypeScript
- **Build Tool:** Vite 5.0+
- **Styling:** Tailwind CSS
- **State Management:** Zustand + TanStack Query
- **Routing:** React Router v6

### Backend
- **Runtime:** Node.js 20
- **Framework:** Express.js
- **Database:** Supabase PostgreSQL
- **Cache:** Redis
- **File Processing:** FFmpeg

## 📚 Documentation

- [Structure Recommendations](./docs/structure_recommendations.md)
- [2-Week Implementation Plan](./docs/2_week_implementation_plan.md)
- [Optimized Tech Stack](./docs/optimized_tech_stack.md)

## 🚀 Deployment

### Frontend
Deploy to Netlify, Vercel, or serve from nginx

### Backend
Deploy to VPS with PM2 process management

```bash
# Production deployment
npm run build
pm2 start ecosystem.config.js
```

## 📊 Performance Targets

- **Build Time:** < 500ms (Vite)
- **Bundle Size:** < 120KB gzipped
- **API Response:** < 500ms
- **Test Coverage:** > 80%

## 🤝 Contributing

1. Follow feature-based architecture
2. Write tests for new features
3. Update documentation
4. Follow TypeScript best practices

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.
