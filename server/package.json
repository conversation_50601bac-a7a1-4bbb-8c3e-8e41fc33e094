{"name": "sela-web-server", "version": "1.0.0", "description": "SELA Web API Server - VPS Backend", "type": "module", "main": "dist/server.js", "scripts": {"dev": "tsx watch src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "vitest", "lint": "eslint src --ext .ts", "type-check": "tsc --noEmit"}, "keywords": ["api", "express", "supabase", "vps", "typescript"], "author": "SELA Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "dotenv": "^16.3.1", "@supabase/supabase-js": "^2.38.5", "redis": "^4.6.10", "bull": "^4.12.2", "multer": "^1.4.5-lts.1", "winston": "^3.11.0", "zod": "^3.22.4", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/multer": "^1.4.11", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/node": "^20.9.0", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "eslint": "^8.53.0", "typescript": "^5.2.2", "tsx": "^4.1.4", "vitest": "^0.34.6"}}