import { NavLink } from 'react-router-dom'
import { 
  Home, 
  BookOpen, 
  BarChart3, 
  User, 
  Settings,
  PlayCircle
} from 'lucide-react'
import { cn } from '@/shared/utils/cn'

const navigation = [
  { name: 'Dashboard', href: '/app', icon: Home },
  { name: 'Courses', href: '/app/courses', icon: BookOpen },
  { name: 'Progress', href: '/app/progress', icon: BarChart3 },
  { name: 'Video Player', href: '/app/player', icon: PlayCircle },
  { name: 'Profile', href: '/app/profile', icon: User },
  { name: 'Settings', href: '/app/settings', icon: Settings },
]

export function Sidebar() {
  return (
    <div className="w-64 bg-white shadow-sm border-r border-gray-200 min-h-screen">
      <nav className="mt-8 px-4">
        <ul className="space-y-2">
          {navigation.map((item) => (
            <li key={item.name}>
              <NavLink
                to={item.href}
                end={item.href === '/app'}
                className={({ isActive }) =>
                  cn(
                    'group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',
                    isActive
                      ? 'bg-primary-50 text-primary-700 border-r-2 border-primary-700'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  )
                }
              >
                <item.icon
                  className="mr-3 h-5 w-5 flex-shrink-0"
                  aria-hidden="true"
                />
                {item.name}
              </NavLink>
            </li>
          ))}
        </ul>
      </nav>
    </div>
  )
}
