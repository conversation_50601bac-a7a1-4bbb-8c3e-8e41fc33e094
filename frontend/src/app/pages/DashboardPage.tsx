import { BookOpen, Users, TrendingUp, Clock } from 'lucide-react'

export function DashboardPage() {
  const stats = [
    {
      name: 'Enrolled Courses',
      value: '12',
      icon: BookOpen,
      change: '+2 this month',
      changeType: 'positive' as const,
    },
    {
      name: 'Completed Courses',
      value: '8',
      icon: TrendingUp,
      change: '+3 this month',
      changeType: 'positive' as const,
    },
    {
      name: 'Study Hours',
      value: '47',
      icon: Clock,
      change: '+12 this week',
      changeType: 'positive' as const,
    },
    {
      name: 'Community Rank',
      value: '#156',
      icon: Users,
      change: '+23 positions',
      changeType: 'positive' as const,
    },
  ]

  const recentCourses = [
    {
      id: 1,
      title: 'React Advanced Patterns',
      progress: 75,
      lastAccessed: '2 hours ago',
      thumbnail: 'https://via.placeholder.com/100x60',
    },
    {
      id: 2,
      title: 'TypeScript Fundamentals',
      progress: 45,
      lastAccessed: '1 day ago',
      thumbnail: 'https://via.placeholder.com/100x60',
    },
    {
      id: 3,
      title: 'Node.js Backend Development',
      progress: 90,
      lastAccessed: '3 days ago',
      thumbnail: 'https://via.placeholder.com/100x60',
    },
  ]

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600">Welcome back! Here's your learning progress.</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat) => (
          <div key={stat.name} className="card p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center">
                  <stat.icon className="h-5 w-5 text-primary-600" />
                </div>
              </div>
              <div className="ml-4 flex-1">
                <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                <p className="text-2xl font-semibold text-gray-900">{stat.value}</p>
              </div>
            </div>
            <div className="mt-4">
              <span className="text-sm text-green-600">{stat.change}</span>
            </div>
          </div>
        ))}
      </div>

      {/* Recent Courses */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Continue Learning
          </h3>
          <div className="space-y-4">
            {recentCourses.map((course) => (
              <div key={course.id} className="flex items-center space-x-4">
                <img
                  src={course.thumbnail}
                  alt={course.title}
                  className="w-16 h-10 rounded object-cover"
                />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {course.title}
                  </p>
                  <p className="text-sm text-gray-500">{course.lastAccessed}</p>
                  <div className="mt-2">
                    <div className="bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-primary-600 h-2 rounded-full"
                        style={{ width: `${course.progress}%` }}
                      />
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      {course.progress}% complete
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Learning Goals */}
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            This Week's Goals
          </h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Complete 3 lessons</span>
              <span className="text-sm font-medium text-green-600">2/3</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Study 10 hours</span>
              <span className="text-sm font-medium text-yellow-600">7/10</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Take 2 quizzes</span>
              <span className="text-sm font-medium text-green-600">2/2</span>
            </div>
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-900">
                  Weekly Progress
                </span>
                <span className="text-sm font-medium text-primary-600">73%</span>
              </div>
              <div className="mt-2 bg-gray-200 rounded-full h-2">
                <div
                  className="bg-primary-600 h-2 rounded-full"
                  style={{ width: '73%' }}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
