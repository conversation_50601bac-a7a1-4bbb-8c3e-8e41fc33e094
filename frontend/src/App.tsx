import { Routes, Route } from 'react-router-dom'
import { Suspense } from 'react'
import { ErrorBoundary } from '@/shared/components/ErrorBoundary'
import { LoadingSpinner } from '@/shared/components/LoadingSpinner'
import { MainLayout } from '@/app/layouts/MainLayout'

// Lazy load pages for better performance
import { HomePage } from '@/app/pages/HomePage'
import { LoginPage } from '@/features/auth/components/LoginPage'
import { CoursesPage } from '@/features/courses/components/CoursesPage'
import { DashboardPage } from '@/app/pages/DashboardPage'

function App() {
  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-gray-50">
        <Suspense fallback={<LoadingSpinner />}>
          <Routes>
            {/* Public routes */}
            <Route path="/" element={<HomePage />} />
            <Route path="/login" element={<LoginPage />} />
            
            {/* Protected routes with layout */}
            <Route path="/app" element={<MainLayout />}>
              <Route index element={<DashboardPage />} />
              <Route path="courses" element={<CoursesPage />} />
              <Route path="courses/:id" element={<div>Course Detail</div>} />
              <Route path="progress" element={<div>Progress Page</div>} />
              <Route path="profile" element={<div>Profile Page</div>} />
            </Route>
            
            {/* 404 page */}
            <Route path="*" element={<div>404 - Page Not Found</div>} />
          </Routes>
        </Suspense>
      </div>
    </ErrorBoundary>
  )
}

export default App
