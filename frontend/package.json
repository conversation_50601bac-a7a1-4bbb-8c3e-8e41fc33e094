{"name": "sela-web-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "@supabase/supabase-js": "^2.38.5", "@tanstack/react-query": "^5.8.4", "zustand": "^4.4.7", "react-hook-form": "^7.48.2", "zod": "^3.22.4", "@hookform/resolvers": "^3.3.2", "react-player": "^2.13.0", "date-fns": "^2.30.0", "lucide-react": "^0.294.0", "@headlessui/react": "^1.7.17", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "typescript": "^5.2.2", "vite": "^5.0.0", "vitest": "^0.34.6", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.4", "tailwindcss": "^3.3.5", "autoprefixer": "^10.4.16", "postcss": "^8.4.31"}}